import { useRef, useState, useCallback, useMemo, useEffect } from 'react';
import { createPortal } from 'react-dom';
import { Track } from 'livekit-client';
import { VideoTrack, useTracks, isTrackReference, useParticipants, TrackToggle, DisconnectButton } from '@livekit/components-react';
import { PhoneFilled } from '@ant-design/icons';
import { generateAvatar, parseMetadata } from '../utils/helper';
import { ScreenCaptureButton } from '../components/settings/ScreenCapture/ScreenCaptureButton';
import './PictureInPicture.scss';

// Speaking priority configuration
const SPEAKING_PRIORITY_CONFIG = {
  STICKY_DURATION: 5000, // Keep speaking participants visible for 5 seconds after they stop speaking
  UPDATE_INTERVAL: 500,   // Check for speaking changes every 500ms
};

function SimplePipContent({
  localParticipant,
  controlPosition = 'bottom',
  room,
  setToastNotification,
  setToastStatus,
  setShowToast,
  isHost,
  isCoHost
}) {

  const allParticipants = useParticipants();

  // State for speaking priority tracking
  const [speakingHistory, setSpeakingHistory] = useState(new Map()); 

  // Get camera and screen share tracks for video display
  const allTracks = useTracks([
    { source: Track.Source.Camera, withPlaceholder: true },
    { source: Track.Source.ScreenShare, withPlaceholder: false },
  ]);

  // Find local camera track
  const localCameraTrack = allTracks
    .filter(isTrackReference)
    .find((track) =>
      track.participant.isLocal &&
      track.source === Track.Source.Camera
    );


  const screenShareTracks = allTracks
    .filter(isTrackReference)
    .filter((track) => track.publication.source === Track.Source.ScreenShare);


  const isScreenShareActive = screenShareTracks.some((track) =>
    track.publication.isSubscribed && !track.publication.isMuted
  );

  const activeScreenShareTrack = screenShareTracks.find((track) =>
    track.publication.isSubscribed && !track.publication.isMuted
  );


  useEffect(() => {
    const now = Date.now();
    const currentSpeaking = allParticipants.filter(p => p.isSpeaking);

    if (currentSpeaking.length > 0) {
      setSpeakingHistory(prev => {
        const updated = new Map(prev);
        currentSpeaking.forEach(p => {
          updated.set(p.identity, now);
        });
        return updated;
      });
    }
  }, [allParticipants.map(p => `${p.identity}-${p.isSpeaking}`).join(',')]);

  // Advanced Speaking Priority System with Sticky Behavior
  const getAdvancedSpeakingPriorityParticipants = useCallback((participants, maxCount) => {
    const now = Date.now();
    const currentSpeaking = participants.filter(p => p.isSpeaking);
    const currentSpeakingIds = new Set(currentSpeaking.map(p => p.identity));

    // Get recently speaking participants (within sticky duration)
    const recentlySpeaking = participants.filter(p => {
      const lastSpeakingTime = speakingHistory.get(p.identity);
      return lastSpeakingTime && (now - lastSpeakingTime) <= SPEAKING_PRIORITY_CONFIG.STICKY_DURATION;
    });

    // Priority order: Currently speaking > Recently speaking > Others
    const currentlySpakingParticipants = participants.filter(p => currentSpeakingIds.has(p.identity));
    const recentlySpokingParticipants = recentlySpeaking.filter(p => !currentSpeakingIds.has(p.identity));
    const otherParticipants = participants.filter(p =>
      !currentSpeakingIds.has(p.identity) &&
      !recentlySpeaking.some(rp => rp.identity === p.identity)
    );

    // Build final list with priority
    const prioritizedList = [
      ...currentlySpakingParticipants,
      ...recentlySpokingParticipants,
      ...otherParticipants
    ];

    // Return up to maxCount participants
    return prioritizedList.slice(0, maxCount);
  }, [speakingHistory]);

  // Periodic update for speaking priority (cleanup old speaking history)
  useEffect(() => {
    const interval = setInterval(() => {
      const now = Date.now();
      setSpeakingHistory(prev => {
        const updated = new Map();
        prev.forEach((lastSpeakingTime, participantId) => {
          // Keep only recent speaking history
          if ((now - lastSpeakingTime) <= SPEAKING_PRIORITY_CONFIG.STICKY_DURATION * 2) {
            updated.set(participantId, lastSpeakingTime);
          }
        });
        return updated;
      });
    }, SPEAKING_PRIORITY_CONFIG.UPDATE_INTERVAL);

    return () => clearInterval(interval);
  }, [setSpeakingHistory]);

  // Get remote participants with advanced speaking priority
  // Enhanced: Dynamic max remotes based on screen sharing and PiP window size
  const getMaxRemotes = (hasScreenShare) => {
    if (hasScreenShare) {
      // With screen share: more conservative to keep screen share prominent
      return Math.min(allParticipants.length - 1, 4); // Max 4 remotes when screen sharing
    } else {
      // Without screen share: can show more participants
      return Math.min(allParticipants.length - 1, 6); // Max 6 remotes when no screen share
    }
  };

  const maxRemotes = getMaxRemotes(isScreenShareActive);
  const allRemoteParticipants = allParticipants.filter((participant) => !participant.isLocal);
  const remoteParticipants = getAdvancedSpeakingPriorityParticipants(allRemoteParticipants, maxRemotes);

  // Create remote participant data with their camera tracks (if any)
  const remoteParticipantsWithTracks = remoteParticipants.map((participant) => {
    const cameraTrack = allTracks
      .filter(isTrackReference)
      .find((track) =>
        track.participant.identity === participant.identity &&
        track.source === Track.Source.Camera
      );

    return {
      participant,
      track: cameraTrack || null
    };
  });

  // Check if local participant is speaking
  const isSpeaking = localParticipant?.isSpeaking || false;

  // Get participant count for dynamic grid (now based on all remote participants)
  const participantCount = remoteParticipants.length;

  // Auto-fitting grid system - one universal grid that adapts to any participant count
  const getGridClass = (controlPos, hasScreenShare) => {
    const baseClass = controlPos === 'top' ? 'control-top' : 'control-bottom';
    const gridType = hasScreenShare ? 'pip-grid-screenshare-auto' : 'pip-grid-auto';
    return `${gridType} ${baseClass}`;
  };

  // Get participant area attributes for auto-fitting
  const getParticipantAreaAttributes = (count, hasScreenShare) => {
    if (hasScreenShare) {
      // Screen share participant attributes
      if (count <= 4) {
        return { 'data-screenshare-participants': count.toString() };
      } else {
        return { 'data-screenshare-participants-many': 'true' };
      }
    } else {
      // Regular participant attributes
      if (count <= 6) {
        return { 'data-participants': count.toString() };
      } else {
        return { 'data-participants-many': 'true' };
      }
    }
  };


  const avatarName = localParticipant?.name
    ? generateAvatar(localParticipant.name)
    : generateAvatar(localParticipant.identity);
  let avatarColor = '#7C4DFF';
  try {
    const metaColor = parseMetadata(localParticipant?.metadata)?.color;
    if (metaColor) avatarColor = metaColor;
  } catch (e) { /* ignore */ }


  const getRemoteParticipantInfo = (participant) => {
    const name = participant?.name
      ? generateAvatar(participant.name)
      : generateAvatar(participant.identity);
    let color = '#7C4DFF';
    try {
      const metaColor = parseMetadata(participant?.metadata)?.color;
      if (metaColor) color = metaColor;
    } catch (e) { /* ignore */ }
    return { name, color };
  };


  const ControlTile = useMemo(() => {
    return (
      <div className="pip-tile pip-control-tile">
        <div className="pip-control-content">
          <div className="pip-control-buttons">
            <TrackToggle
              source={Track.Source.Microphone}
              showIcon
              className="pip-control-button"
            />
            <TrackToggle
              source={Track.Source.Camera}
              showIcon
              className="pip-control-button"
            />
            {isScreenShareActive && (
              <TrackToggle
                source={Track.Source.ScreenShare}
                showIcon
                className="pip-control-button"
              />
            )}
            <DisconnectButton
              className="pip-control-button pip-disconnect-button"
            >
              <PhoneFilled />
            </DisconnectButton>
          </div>
        </div>
      </div>
    );
  }, [isScreenShareActive]); // Depend on isScreenShareActive to update when screen share status changes

  // Calculate total participants for auto-fitting (local + remotes)
  const totalParticipants = 1 + participantCount; // +1 for local participant
  const participantAreaAttrs = getParticipantAreaAttributes(totalParticipants, isScreenShareActive);

  return (
    <div className="pip-main-container">
      <div className={`pip-grid-container ${getGridClass(controlPosition, isScreenShareActive)}`}>

        {/* Control tile - positioned at top if controlPosition is 'top' */}
        {controlPosition === 'top' && ControlTile}

        {isScreenShareActive ? (
          <>
            {/* Screen Share Tile - Always prominent */}
            <div className="pip-screenshare-area">
              <div className="pip-tile pip-tile-screenshare">
                {activeScreenShareTrack ? (
                  <div className="pip-video-container pip-screenshare-video">
                    <VideoTrack
                      trackRef={activeScreenShareTrack}
                      style={{
                        width: '100%',
                        height: '100%',
                        objectFit: 'contain',
                        borderRadius: '2vmin'
                      }}
                    />

                    {(isHost || isCoHost) && (
                      <div className="pip-screen-capture-button">
                        <ScreenCaptureButton
                          room={room}
                          screenShareTracks={screenShareTracks}
                          focusTrack={activeScreenShareTrack}
                          setToastNotification={setToastNotification}
                          setToastStatus={setToastStatus}
                          setShowToast={setShowToast}
                          setShowPopover={() => {}} // No popover in PiP
                        />
                      </div>
                    )}
                  </div>
                ) : (
                  <div className="pip-tile-center-dot" />
                )}
              </div>
            </div>

            {/* Auto-fitting participants area for screen sharing */}
            <div className="pip-screenshare-participants-area" {...participantAreaAttrs}>
              {/* Local Participant Tile */}
              <div className={`pip-tile pip-tile-small pip-tile-local ${isSpeaking ? 'pip-tile-speaking' : ''}`}>
                {/* MEMORY OPTIMIZATION: Always show avatar for local participant when screen sharing */}
                <div className="pip-tile-avatar-center">
                  <div
                    className="pip-tile-avatar pip-tile-avatar-small"
                    style={{ backgroundColor: avatarColor }}
                  >
                    {avatarName}
                  </div>
                </div>
                {isSpeaking && (
                  <>
                    <div style={{
                      position: 'absolute',
                      bottom: '0.5vmin',
                      left: '0.5vmin',
                      width: '1vmin',
                      height: '1vmin',
                      background: '#1e8cfa',
                      borderRadius: '50%',
                      zIndex: 2,
                      animation: 'pip-corner-pulse 1.8s cubic-bezier(0.4, 0, 0.6, 1) infinite',
                      animationDelay: '0.6s'
                    }} />
                    <div style={{
                      position: 'absolute',
                      bottom: '0.5vmin',
                      right: '0.5vmin',
                      width: '1vmin',
                      height: '1vmin',
                      background: '#1e8cfa',
                      borderRadius: '50%',
                      zIndex: 2,
                      animation: 'pip-corner-pulse 1.8s cubic-bezier(0.4, 0, 0.6, 1) infinite',
                      animationDelay: '0.9s'
                    }} />
                  </>
                )}
              </div>

              {/* Remote participants - auto-fitting */}
              {remoteParticipantsWithTracks.map((participantData, index) => {
                const { participant } = participantData;
                const remoteInfo = getRemoteParticipantInfo(participant);
                const isRemoteSpeaking = participant?.isSpeaking || false;

                return (
                  <div
                    key={participant.identity || `remote-screenshare-${index}`}
                    className={`pip-tile pip-tile-small pip-tile-remote ${isRemoteSpeaking ? 'pip-tile-speaking' : ''}`}
                  >
                    {/* MEMORY OPTIMIZATION: Always show avatar for remote participants when screen sharing */}
                    <div className="pip-tile-avatar-center">
                      <div
                        className="pip-tile-avatar pip-tile-avatar-small"
                        style={{ backgroundColor: remoteInfo.color }}
                      >
                        {remoteInfo.name}
                      </div>
                    </div>

                    {/* Corner accent lights for remote speaking */}
                    {isRemoteSpeaking && (
                      <>
                        <div style={{
                          position: 'absolute',
                          bottom: '0.5vmin',
                          left: '0.5vmin',
                          width: '1vmin',
                          height: '1vmin',
                          background: '#1e8cfa',
                          borderRadius: '50%',
                          zIndex: 2,
                          animation: 'pip-corner-pulse 1.8s cubic-bezier(0.4, 0, 0.6, 1) infinite',
                          animationDelay: '0.6s'
                        }} />
                        <div style={{
                          position: 'absolute',
                          bottom: '0.5vmin',
                          right: '0.5vmin',
                          width: '1vmin',
                          height: '1vmin',
                          background: '#1e8cfa',
                          borderRadius: '50%',
                          zIndex: 2,
                          animation: 'pip-corner-pulse 1.8s cubic-bezier(0.4, 0, 0.6, 1) infinite',
                          animationDelay: '0.9s'
                        }} />
                      </>
                    )}
                  </div>
                );
              })}
            </div>
          </>
        ) : (
          // NO SCREEN SHARE: Auto-fitting participants area
          <div className="pip-participants-area" {...participantAreaAttrs}>
            {/* Local Participant Tile */}
            <div className={`pip-tile pip-tile-main ${isSpeaking ? 'pip-tile-speaking' : ''}`}>
              {localCameraTrack?.publication && !localCameraTrack.publication.isMuted ? (
                // Show video when camera is on
                <div className="pip-video-container pip-camera-video">
                  <VideoTrack
                    trackRef={localCameraTrack}
                    style={{
                      width: '100%',
                      height: '100%',
                      objectFit: 'cover',
                      borderRadius: '2vmin'
                    }}
                  />
                </div>
              ) : (
                // Show avatar when camera is off
                <div className="pip-tile-avatar-center">
                  <div
                    className="pip-tile-avatar pip-tile-avatar-small"
                    style={{ backgroundColor: avatarColor }}
                  >
                    {avatarName}
                  </div>
                </div>
              )}

              {/* Corner accent lights for local speaking */}
              {isSpeaking && (
                <>
                  <div style={{
                    position: 'absolute',
                    bottom: '1vmin',
                    left: '1vmin',
                    width: '1.5vmin',
                    height: '1.5vmin',
                    background: '#1e8cfa',
                    borderRadius: '50%',
                    zIndex: 2,
                    animation: 'pip-corner-pulse 1.8s cubic-bezier(0.4, 0, 0.6, 1) infinite',
                    animationDelay: '0.6s'
                  }} />
                  <div style={{
                    position: 'absolute',
                    bottom: '1vmin',
                    right: '1vmin',
                    width: '1.5vmin',
                    height: '1.5vmin',
                    background: '#1e8cfa',
                    borderRadius: '50%',
                    zIndex: 2,
                    animation: 'pip-corner-pulse 1.8s cubic-bezier(0.4, 0, 0.6, 1) infinite',
                    animationDelay: '0.9s'
                  }} />
                </>
              )}
            </div>

            {/* Remote participants - auto-fitting */}
            {remoteParticipantsWithTracks.map((participantData, index) => {
              const { participant, track } = participantData;
              const remoteInfo = getRemoteParticipantInfo(participant);
              const isRemoteSpeaking = participant?.isSpeaking || false;

              return (
                <div
                  key={participant.identity || `remote-${index}`}
                  className={`pip-tile pip-tile-small pip-tile-remote ${isRemoteSpeaking ? 'pip-tile-speaking' : ''}`}
                >
                  {track?.publication && !track.publication.isMuted ? (
                    // Show remote video
                    <div className="pip-video-container pip-camera-video">
                      <VideoTrack
                        trackRef={track}
                        style={{
                          width: '100%',
                          height: '100%',
                          objectFit: 'cover',
                          borderRadius: '2vmin'
                        }}
                      />
                    </div>
                  ) : (
                    // Show remote avatar when camera is off
                    <div className="pip-tile-avatar-center">
                      <div
                        className="pip-tile-avatar pip-tile-avatar-small"
                        style={{ backgroundColor: remoteInfo.color }}
                      >
                        {remoteInfo.name}
                      </div>
                    </div>
                  )}

                  {/* Corner accent lights for remote speaking */}
                  {isRemoteSpeaking && (
                    <>
                      <div style={{
                        position: 'absolute',
                        bottom: '0.5vmin',
                        left: '0.5vmin',
                        width: '1vmin',
                        height: '1vmin',
                        background: '#1e8cfa',
                        borderRadius: '50%',
                        zIndex: 2,
                        animation: 'pip-corner-pulse 1.8s cubic-bezier(0.4, 0, 0.6, 1) infinite',
                        animationDelay: '0.6s'
                      }} />
                      <div style={{
                        position: 'absolute',
                        bottom: '0.5vmin',
                        right: '0.5vmin',
                        width: '1vmin',
                        height: '1vmin',
                        background: '#1e8cfa',
                        borderRadius: '50%',
                        zIndex: 2,
                        animation: 'pip-corner-pulse 1.8s cubic-bezier(0.4, 0, 0.6, 1) infinite',
                        animationDelay: '0.9s'
                      }} />
                    </>
                  )}
                </div>
              );
            })}
          </div>
        )}

        {/* Control tile - positioned at bottom if controlPosition is 'bottom' */}
        {controlPosition === 'bottom' && ControlTile}
      </div>
    </div>
  );
}

export function usePictureInPicture({
  setIsPIPEnabled,
  setToastNotification,
  setToastStatus,
  setShowToast,
  localParticipant,
  room,
  controlPosition = 'top',
  isHost,
  isCoHost
}) {
  const pipWindowRef = useRef(null);
  const pipContainerRef = useRef(null);
  const [pipWindowDocument, setPipWindowDocument] = useState(null);

  // Get participant count for dynamic sizing
  const allTracks = useTracks([
    { source: Track.Source.Camera, withPlaceholder: true },
  ]);
  const remoteTracks = allTracks
    .filter(isTrackReference)
    .filter((track) => !track.participant.isLocal);
  const participantCount = remoteTracks.length;

  // Auto-fitting PiP window configuration - more conservative sizing since tiles auto-adjust
  const getDynamicConfig = (count) => {
    // Since tiles auto-fit, we can use more conservative window sizes
    switch(count) {
      case 0: // Solo - minimum size
        return { width: 180, height: 297 };
      case 1: // Two participants - small window
        return { width: 220, height: 363 };
      case 2: // Three participants - medium window
        return { width: 260, height: 429 };
      case 3: // Four participants - larger window
        return { width: 300, height: 495 };
      case 4: // Five participants - extra large window
        return { width: 340, height: 561 };
      case 5: // Six participants - maximum window
        return { width: 380, height: 627 };
      default: // Many participants - largest window with auto-fitting and scrolling
        return { width: 420, height: 693 };
    }
  };

  const defaultConfig = useMemo(() => getDynamicConfig(participantCount), [participantCount]);

  // Track previous participant count for notifications
  const prevParticipantCountRef = useRef(participantCount);

  // Auto-resize PiP window when participant count changes
  useEffect(() => {
    if (pipWindowRef.current && pipWindowDocument) {
      const newConfig = getDynamicConfig(participantCount);
      const prevCount = prevParticipantCountRef.current;

      try {
        // Resize the existing PiP window
        pipWindowRef.current.resizeTo(newConfig.width, newConfig.height);

        // Show notification about participant change (only if PiP is active)
        if (prevCount !== participantCount && prevCount !== undefined) {
          const participantChange = participantCount > prevCount ? 'joined' : 'left';
          const totalParticipants = participantCount + 1; // +1 for local participant

          setToastNotification(`Participant ${participantChange}. PiP adjusted for ${totalParticipants} participant${totalParticipants > 1 ? 's' : ''}.`);
          setToastStatus("info");
          setShowToast(true);
        }

      } catch (error) {
        console.log('PiP resize not supported on this browser');
      }
    }

    // Update previous count
    prevParticipantCountRef.current = participantCount;
  }, [participantCount, pipWindowDocument, setToastNotification, setToastStatus, setShowToast]);

  // Check Document PiP support
  const isSupported = useMemo(() => {
    return 'documentPictureInPicture' in window;
  }, []);

  // Close PiP window
  const closePipWindow = useCallback(() => {
    if (pipWindowRef.current) {
      pipWindowRef.current.close();
      pipWindowRef.current = null;
    }
    setPipWindowDocument(null);
    pipContainerRef.current = null;
    setIsPIPEnabled(false);
  }, [setIsPIPEnabled]);

  // Simple PiP Content
  const PipContent = useCallback(() => {
    return <SimplePipContent
      localParticipant={localParticipant}
      controlPosition={controlPosition}
      room={room}
      setToastNotification={setToastNotification}
      setToastStatus={setToastStatus}
      setShowToast={setShowToast}
      isHost={isHost}
      isCoHost={isCoHost}
    />;
  }, [localParticipant, controlPosition, room, setToastNotification, setToastStatus, setShowToast, isHost, isCoHost]);




  // Simple error handling
  const handlePipError = useCallback(() => {
    setToastNotification("Failed to open Picture-in-Picture");
    setToastStatus("error");
    setShowToast(true);
  }, [setToastNotification, setToastStatus, setShowToast]);

  // Simple PiP window opening
  const openPipWindow = useCallback(async () => {
    if (!isSupported) {
      handlePipError(new Error('Document Picture-in-Picture not supported'));
      return false;
    }

    if (pipWindowRef.current) {
      return true;
    }

    try {
      const pipWindow = await window.documentPictureInPicture.requestWindow({
        width: defaultConfig.width,
        height: defaultConfig.height,
      });

      pipWindowRef.current = pipWindow;
      setPipWindowDocument(pipWindow.document);
      setIsPIPEnabled(true);

      // Setup document and copy styles from main document
      const pipDoc = pipWindow.document;

      // Copy all stylesheets from the main document to PiP window
      const mainStyleSheets = Array.from(document.styleSheets);
      mainStyleSheets.forEach((styleSheet) => {
        try {
          if (styleSheet.href) {
            // External stylesheet
            const link = pipDoc.createElement('link');
            link.rel = 'stylesheet';
            link.href = styleSheet.href;
            pipDoc.head.appendChild(link);
          } else if (styleSheet.ownerNode) {
            // Inline stylesheet
            const clonedStyle = styleSheet.ownerNode.cloneNode(true);
            pipDoc.head.appendChild(clonedStyle);
          }
        } catch (e) {
          // Handle CORS issues with external stylesheets
          console.warn('Could not copy stylesheet:', e);
        }
      });

      const container = pipDoc.createElement('div');
      container.id = 'pip-root';
      pipDoc.body.appendChild(container);
      pipContainerRef.current = container;

      // Simple close handler
      pipWindow.addEventListener('pagehide', () => {
        closePipWindow();
      });

      return true;
    } catch (error) {
      handlePipError(error);
      return false;
    }
  }, [isSupported, defaultConfig, setIsPIPEnabled, closePipWindow, handlePipError]);

  // Toggle PiP mode
  const togglePipMode = useCallback(async (enabled) => {
    if (enabled) {
      return openPipWindow();
    } else {
      closePipWindow();
      return true;
    }
  }, [openPipWindow, closePipWindow]);

  // Simple PiP content rendering
  const pipPortal = useMemo(() => {
    if (!pipWindowDocument || !pipContainerRef.current) return null;

    return createPortal(
      <div className="pip-container">
        <PipContent />
      </div>,
      pipContainerRef.current
    );
  }, [pipWindowDocument, PipContent]);

  return {
    togglePipMode,
    pipPortal,
    isSupported,
    controlPosition 
  };
}